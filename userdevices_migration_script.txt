-- =====================================================
-- USERDEVICES TABLOSU ÖZEL MİGRASYON SCRİPTİ
-- =====================================================

-- 1. MEVCUT SÜTUN YAPISINI KONTROL ET
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'UserDevices' 
ORDER BY ORDINAL_POSITION;

-- 2. ÖZEL SÜTUNLARI KONTROL ET
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'UserDevices' AND COLUMN_NAME = 'CreatedAt') 
        THEN 'CreatedAt MEVCUT' 
        ELSE 'CreatedAt YOK' 
    END AS CreatedAt_Durumu,
    CASE 
        WHEN EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'UserDevices' AND COLUMN_NAME = 'CreationDate') 
        THEN 'CreationDate MEVCUT' 
        ELSE 'CreationDate YOK' 
    END AS CreationDate_Durumu,
    CASE 
        WHEN EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'UserDevices' AND COLUMN_NAME = 'LastUsedAt') 
        THEN 'LastUsedAt MEVCUT' 
        ELSE 'LastUsedAt YOK' 
    END AS LastUsedAt_Durumu;

-- =====================================================
-- SENARYO 1: CreatedAt VAR, CreationDate YOK
-- =====================================================
-- Bu durumda CreatedAt'i CreationDate'e rename et
/*
IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'UserDevices' AND COLUMN_NAME = 'CreatedAt')
AND NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'UserDevices' AND COLUMN_NAME = 'CreationDate')
BEGIN
    EXEC sp_rename 'UserDevices.CreatedAt', 'CreationDate', 'COLUMN';
    ALTER TABLE UserDevices ALTER COLUMN CreationDate DATETIME2 NULL;
    PRINT 'CreatedAt sütunu CreationDate olarak yeniden adlandırıldı';
END
*/

-- =====================================================
-- SENARYO 2: HEM CreatedAt HEM CreationDate VAR
-- =====================================================
-- Bu durumda CreatedAt'teki verileri CreationDate'e kopyala ve CreatedAt'i sil
/*
IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'UserDevices' AND COLUMN_NAME = 'CreatedAt')
AND EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'UserDevices' AND COLUMN_NAME = 'CreationDate')
BEGIN
    -- Önce CreationDate NULL olanları CreatedAt ile doldur
    UPDATE UserDevices 
    SET CreationDate = CreatedAt 
    WHERE CreationDate IS NULL AND CreatedAt IS NOT NULL;
    
    -- CreatedAt sütununu sil
    ALTER TABLE UserDevices DROP COLUMN CreatedAt;
    PRINT 'CreatedAt verileri CreationDate''e kopyalandı ve CreatedAt sütunu silindi';
END
*/

-- =====================================================
-- SENARYO 3: SADECE CreationDate VAR (İDEAL DURUM)
-- =====================================================
-- Bu durumda hiçbir şey yapma, zaten doğru

-- =====================================================
-- SENARYO 4: HİÇBİRİ YOK
-- =====================================================
-- Bu durumda CreationDate ekle
/*
IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'UserDevices' AND COLUMN_NAME = 'CreationDate')
AND NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'UserDevices' AND COLUMN_NAME = 'CreatedAt')
BEGIN
    ALTER TABLE UserDevices ADD CreationDate DATETIME2 NULL;
    PRINT 'CreationDate sütunu eklendi';
END
*/

-- =====================================================
-- GENEL DÜZENLEMELER
-- =====================================================

-- IsActive'i nullable yap (eğer değilse)
IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
           WHERE TABLE_NAME = 'UserDevices' AND COLUMN_NAME = 'IsActive' 
           AND IS_NULLABLE = 'NO')
BEGIN
    ALTER TABLE UserDevices ALTER COLUMN IsActive BIT NULL;
    PRINT 'IsActive nullable yapıldı';
END

-- LastUsedAt'i nullable yap (eğer değilse)
IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
           WHERE TABLE_NAME = 'UserDevices' AND COLUMN_NAME = 'LastUsedAt' 
           AND IS_NULLABLE = 'NO')
BEGIN
    ALTER TABLE UserDevices ALTER COLUMN LastUsedAt DATETIME2 NULL;
    PRINT 'LastUsedAt nullable yapıldı';
END

-- UpdatedDate ekle (eğer yoksa)
IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'UserDevices' AND COLUMN_NAME = 'UpdatedDate')
BEGIN
    ALTER TABLE UserDevices ADD UpdatedDate DATETIME2 NULL;
    PRINT 'UpdatedDate sütunu eklendi';
END

-- DeletedDate ekle (eğer yoksa)
IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'UserDevices' AND COLUMN_NAME = 'DeletedDate')
BEGIN
    ALTER TABLE UserDevices ADD DeletedDate DATETIME2 NULL;
    PRINT 'DeletedDate sütunu eklendi';
END

-- =====================================================
-- VERİ GÜNCELLEMELERİ
-- =====================================================

-- IsActive NULL olanları 1 yap
UPDATE UserDevices SET IsActive = 1 WHERE IsActive IS NULL;
PRINT 'IsActive NULL değerleri 1 yapıldı';

-- =====================================================
-- KONTROL SORGUSU
-- =====================================================

-- Son durumu kontrol et
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'UserDevices' 
ORDER BY ORDINAL_POSITION;

-- Veri sayılarını kontrol et
SELECT 
    COUNT(*) AS ToplamKayit,
    COUNT(CASE WHEN IsActive = 1 THEN 1 END) AS AktifKayit,
    COUNT(CASE WHEN IsActive = 0 THEN 1 END) AS PasifKayit,
    COUNT(CASE WHEN IsActive IS NULL THEN 1 END) AS NullKayit
FROM UserDevices;

-- =====================================================
-- KULLANIM TALİMATI:
-- =====================================================
-- 1. Önce kontrol sorgularını çalıştır
-- 2. Hangi senaryoda olduğunu belirle
-- 3. İlgili senaryo bloğundaki yorumları kaldırıp çalıştır
-- 4. Genel düzenlemeleri çalıştır
-- 5. Son kontrol sorgularını çalıştır
-- =====================================================
