-- =====================================================
-- VERİTABANI MİGRASYON SCRİPTLERİ
-- Soft Delete Alanları Ekleme ve Güncelleme
-- =====================================================

-- =====================================================
-- 1. YEN<PERSON> SÜTUNLAR EKLENECEK TABLOLAR
-- =====================================================

-- Cities tablosu - Tüm soft delete alanları ekleniyor
ALTER TABLE Cities ADD IsActive BIT NULL;
ALTER TABLE Cities ADD CreationDate DATETIME2 NULL;
ALTER TABLE Cities ADD UpdatedDate DATETIME2 NULL;
ALTER TABLE Cities ADD DeletedDate DATETIME2 NULL;

-- Towns tablosu - Tüm soft delete alanları ekleniyor
ALTER TABLE Towns ADD IsActive BIT NULL;
ALTER TABLE Towns ADD CreationDate DATETIME2 NULL;
ALTER TABLE Towns ADD UpdatedDate DATETIME2 NULL;
ALTER TABLE Towns ADD DeletedDate DATETIME2 NULL;

-- DebtPayments tablosu - UpdatedDate ve DeletedDate ekleniyor
ALTER TABLE DebtPayments ADD UpdatedDate DATETIME2 NULL;
ALTER TABLE DebtPayments ADD DeletedDate DATETIME2 NULL;

-- RemainingDebts tablosu - UpdatedDate ve DeletedDate ekleniyor
ALTER TABLE RemainingDebts ADD UpdatedDate DATETIME2 NULL;
ALTER TABLE RemainingDebts ADD DeletedDate DATETIME2 NULL;

-- MembershipFreezeHistory tablosu - IsActive, UpdatedDate ve DeletedDate ekleniyor
ALTER TABLE MembershipFreezeHistory ADD IsActive BIT NULL;
ALTER TABLE MembershipFreezeHistory ADD UpdatedDate DATETIME2 NULL;
ALTER TABLE MembershipFreezeHistory ADD DeletedDate DATETIME2 NULL;

-- WorkoutProgramDays tablosu - Tüm soft delete alanları ekleniyor
ALTER TABLE WorkoutProgramDays ADD IsActive BIT NULL;
ALTER TABLE WorkoutProgramDays ADD CreationDate DATETIME2 NULL;
ALTER TABLE WorkoutProgramDays ADD UpdatedDate DATETIME2 NULL;
ALTER TABLE WorkoutProgramDays ADD DeletedDate DATETIME2 NULL;

-- WorkoutProgramExercises tablosu - Tüm soft delete alanları ekleniyor
ALTER TABLE WorkoutProgramExercises ADD IsActive BIT NULL;
ALTER TABLE WorkoutProgramExercises ADD CreationDate DATETIME2 NULL;
ALTER TABLE WorkoutProgramExercises ADD UpdatedDate DATETIME2 NULL;
ALTER TABLE WorkoutProgramExercises ADD DeletedDate DATETIME2 NULL;

-- UserDevices tablosu - UpdatedDate ve DeletedDate ekleniyor
-- NOT: Eğer CreatedAt sütunu varsa CreationDate'e rename edilecek
ALTER TABLE UserDevices ADD UpdatedDate DATETIME2 NULL;
ALTER TABLE UserDevices ADD DeletedDate DATETIME2 NULL;

-- =====================================================
-- 2. MEVCUT SÜTUNLARI NULLABLE YAPMA
-- =====================================================

-- Products tablosu - CreationDate nullable yapılıyor
ALTER TABLE Products ALTER COLUMN CreationDate DATETIME2 NULL;

-- Expenses tablosu - CreationDate nullable yapılıyor
ALTER TABLE Expenses ALTER COLUMN CreationDate DATETIME2 NULL;

-- UserLicenses tablosu - CreationDate nullable yapılıyor
ALTER TABLE UserLicenses ALTER COLUMN CreationDate DATETIME2 NULL;

-- Users tablosu - IsActive nullable yapılıyor
ALTER TABLE Users ALTER COLUMN IsActive BIT NULL;

-- UserDevices tablosu - IsActive nullable yapılıyor
ALTER TABLE UserDevices ALTER COLUMN IsActive BIT NULL;

-- DebtPayments tablosu - CreationDate nullable yapılıyor
ALTER TABLE DebtPayments ALTER COLUMN CreationDate DATETIME2 NULL;

-- MembershipFreezeHistory tablosu - CreationDate nullable yapılıyor
ALTER TABLE MembershipFreezeHistory ALTER COLUMN CreationDate DATETIME2 NULL;

-- =====================================================
-- 3. ISACTIVE DEĞERLERİNİ 1 (TRUE) YAPMA
-- =====================================================

-- Yeni eklenen IsActive alanlarını 1 (true) olarak ayarla
UPDATE Cities SET IsActive = 1 WHERE IsActive IS NULL;
UPDATE Towns SET IsActive = 1 WHERE IsActive IS NULL;
UPDATE MembershipFreezeHistory SET IsActive = 1 WHERE IsActive IS NULL;
UPDATE WorkoutProgramDays SET IsActive = 1 WHERE IsActive IS NULL;
UPDATE WorkoutProgramExercises SET IsActive = 1 WHERE IsActive IS NULL;

-- Mevcut tablolardaki NULL IsActive değerlerini 1 yap
UPDATE Users SET IsActive = 1 WHERE IsActive IS NULL;
UPDATE UserDevices SET IsActive = 1 WHERE IsActive IS NULL;
UPDATE Companies SET IsActive = 1 WHERE IsActive IS NULL;
UPDATE CompanyAdresses SET IsActive = 1 WHERE IsActive IS NULL;
UPDATE CompanyUsers SET IsActive = 1 WHERE IsActive IS NULL;
UPDATE Members SET IsActive = 1 WHERE IsActive IS NULL;
UPDATE Memberships SET IsActive = 1 WHERE IsActive IS NULL;
UPDATE MembershipTypes SET IsActive = 1 WHERE IsActive IS NULL;
UPDATE Payments SET IsActive = 1 WHERE IsActive IS NULL;
UPDATE UserCompanies SET IsActive = 1 WHERE IsActive IS NULL;
UPDATE OperationClaims SET IsActive = 1 WHERE IsActive IS NULL;
UPDATE UserOperationClaims SET IsActive = 1 WHERE IsActive IS NULL;
UPDATE Transactions SET IsActive = 1 WHERE IsActive IS NULL;
UPDATE Products SET IsActive = 1 WHERE IsActive IS NULL;
UPDATE EntryExitHistories SET IsActive = 1 WHERE IsActive IS NULL;
UPDATE LicensePackages SET IsActive = 1 WHERE IsActive IS NULL;
UPDATE UserLicenses SET IsActive = 1 WHERE IsActive IS NULL;
UPDATE LicenseTransactions SET IsActive = 1 WHERE IsActive IS NULL;
UPDATE Expenses SET IsActive = 1 WHERE IsActive IS NULL;
UPDATE ExerciseCategories SET IsActive = 1 WHERE IsActive IS NULL;
UPDATE SystemExercises SET IsActive = 1 WHERE IsActive IS NULL;
UPDATE CompanyExercises SET IsActive = 1 WHERE IsActive IS NULL;
UPDATE WorkoutProgramTemplates SET IsActive = 1 WHERE IsActive IS NULL;
UPDATE MemberWorkoutPrograms SET IsActive = 1 WHERE IsActive IS NULL;

-- =====================================================
-- 4. ÖZEL DURUMLAR
-- =====================================================

-- Eğer UserDevices tablosunda CreatedAt sütunu varsa ve CreationDate yoksa:
-- EXEC sp_rename 'UserDevices.CreatedAt', 'CreationDate', 'COLUMN';
-- ALTER TABLE UserDevices ALTER COLUMN CreationDate DATETIME2 NULL;

-- =====================================================
-- 5. KONTROL SORGUSU
-- =====================================================

-- Tüm tabloların soft delete alanlarını kontrol et
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    IS_NULLABLE,
    DATA_TYPE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE COLUMN_NAME IN ('IsActive', 'CreationDate', 'UpdatedDate', 'DeletedDate')
ORDER BY TABLE_NAME, COLUMN_NAME;

-- =====================================================
-- NOTLAR:
-- =====================================================
-- 1. Bu scriptler sırayla çalıştırılmalıdır
-- 2. Backup alınması önerilir
-- 3. Test ortamında önce denenmelidir
-- 4. Tüm entity'ler artık standart soft delete alanlarına sahip olacak
-- 5. Nullable alanlar sayesinde mevcut veriler korunacak
-- =====================================================
