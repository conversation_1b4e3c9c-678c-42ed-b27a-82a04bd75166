Stack trace:
Frame         Function      Args
0007FFFFBBB0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFAAB0) msys-2.0.dll+0x1FE8E
0007FFFFBBB0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBE88) msys-2.0.dll+0x67F9
0007FFFFBBB0  000210046832 (000210286019, 0007FFFFBA68, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBBB0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBBB0  000210068E24 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBE90  00021006A225 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE94E40000 ntdll.dll
7FFE93BA0000 KERNEL32.DLL
7FFE923E0000 KERNELBASE.dll
7FFE940C0000 USER32.dll
7FFE92050000 win32u.dll
7FFE92C10000 GDI32.dll
7FFE92200000 gdi32full.dll
000210040000 msys-2.0.dll
7FFE927D0000 msvcp_win.dll
7FFE92A90000 ucrtbase.dll
7FFE94000000 advapi32.dll
7FFE94A60000 msvcrt.dll
7FFE933B0000 sechost.dll
7FFE92E30000 RPCRT4.dll
7FFE91690000 CRYPTBASE.DLL
7FFE92340000 bcryptPrimitives.dll
7FFE93B60000 IMM32.DLL
